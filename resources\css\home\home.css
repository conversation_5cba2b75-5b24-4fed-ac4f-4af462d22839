* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body,
html {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.flip {
    animation: flip 0.5s ease-in-out;
}

@keyframes flip {
    0% {
        transform: rotateX(0deg);
    }
    50% {
        transform: rotateX(-90deg);
    }
    100% {
        transform: rotateX(0deg);
    }
}

.parent {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-auto-rows: min-content;
    gap: 8px;
}

.div2 {
    grid-column: span 5 / span 5;
    grid-row: span 2 / span 2;
    grid-row-start: 2;
}

.div3 {
    grid-column: span 2 / span 2;
    grid-row-start: 4;
}

.div4 {
    grid-column: span 3 / span 3;
    grid-column-start: 3;
    grid-row-start: 4;
}

.div5 {
    grid-column: span 5 / span 5;
    grid-row-start: 5;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    justify-content: space-between;
    padding: 1rem;
}

.top-section {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.left-info {
    max-width: 50%;
}

.left-info .weather {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}
#iconcuaca {
    width: auto;
    height: auto;
    display: block;
}

.left-info .weather img {
    height: 50px;
    margin-right: 0.5rem;
}
.ketderajat span {
    font-size: 1.2rem;
}

.ketderajat {
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: flex-start;
    font-size: 0.9rem;
    line-height: 1.2;
}

.time {
    padding: 0;
    margin: 0;
    font-size: 5rem;
    color: #5b2991;
    line-height: 1;
    display: flex;
    align-items: stretch;
}

.date {
    line-height: 1;
    font-size: 1.5rem;
    margin-top: 0.3rem;
    color: #5b2991;
}

/* Tablet typography scaling to avoid overflow */
@media (max-width: 1024px) {
    .time { font-size: 3.5rem; }
    .date { font-size: 1.2rem; }
}


.holiday {
    font-size: 1rem;
    font-style: italic;
    margin-bottom: 1rem;
}

/* TOP card - hanya bagian bawah terlihat */
.card-top {
    top: 0;
    opacity: 0.5;
    z-index: 1;
    transform: translateY(-25%); /* geser agar hanya bagian bawah muncul */
}

/* MIDDLE card - utama */
.card-middle {
    top: 50%;
    transform: translateY(-50%); /* posisikan benar-benar di tengah */
    opacity: 1;
    z-index: 3;
}

/* BOTTOM card - hanya bagian atas terlihat */
.card-bottom {
    bottom: 0;
    opacity: 0.5;
    z-index: 1;
    transform: translateY(25%); /* geser agar hanya bagian atas muncul */
}

@keyframes slide {
    0%,
    20% {
        transform: translateX(0);
    }
    33%,
    53% {
        transform: translateX(-100vw);
    }
    66%,
    86% {
        transform: translateX(-200vw);
    }
    100% {
        transform: translateX(0);
    }
}

.info-section {
    gap: 10px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.info-box {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.209);
    flex: 1;
}

.info-box p {
    font-size: 1rem;
}

.info-box ul {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    padding-left: 1rem;
}

.bottom-nav {
    display: flex;
    justify-content: space-around;
    border-radius: 12px;
}

.bottom-nav button {
    background: white;
    border: none;
    border-radius: 10px;
    padding: 0.5rem;
    font-size: 0.8rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    margin: 0 0.3rem;
}

@media (max-width: 1000px) {
    .top-section,
    .info-section {
        flex-direction: column;
    }

    .left-info,
    .right-logo {
        max-width: 100%;
    }

    .mitra-cards {
        flex-direction: column;
    }
}

.btnmenu {
    background: white;
    border-radius: 25px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    margin: 0.5rem;
}

.mitra-cards-stack {
    position: relative;
    height: 100px; /* ini penting agar card bisa ditumpuk */
    width: 100%;
}

.mitra-card {
    position: absolute;
    left: 0;
    width: 100%;
    background: white;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.mitra-card img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

/* Card atas (bagian bawahnya saja yang tampak) */
.card-top {
    top: 0;
    transform: translateY(-30%);
    opacity: 0.5;
    z-index: 1;
}

/* Card tengah (utama) */
.card-middle {
    margin-left: 20px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
    z-index: 3;
}

/* Card bawah (bagian atasnya saja yang tampak) */
.card-bottom {
    bottom: 0;
    transform: translateY(30%);
    opacity: 0.5;
    z-index: 1;
}

/* animasi card */
.slider-wrapper {
    position: relative;
    width: 100%;
}

.slider-track {
    display: flex;
    width: 300%;
    transition: transform 0.5s ease-in-out;
}

.slide {
    width: 100%;
    flex-shrink: 0;
    border-radius: 10px;
}

.slider-dots {
    bottom: 10px;
}

.dot {
    height: 10px;
    width: 10px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    border: none;
}

.dot.active,
.dot:hover {
    background-color: #717171;
}

/* image slide */
.slider-wrapper {
    height: 250px;
}

.slider-track {
    display: flex;
    height: 100%;
}

.slide {
    width: 100%;
    height: 100%;
    flex-shrink: 0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    border: 1px solid #ccc;
}

.slide-image {
    width: max-content;
    height: 100%;
    object-fit: fit;
    /* Ini yang bikin stretch total */
    display: block;
    border-radius: 10px;
}
